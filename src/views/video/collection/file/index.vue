<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <template #toolbar:after>
      <el-button type="default" size="small" @click="handleRefresh">刷新</el-button>
    </template>

    <!-- 文件状态列自定义渲染 -->
    <template #table:value7:simple="{ row }">
      <el-tag :type="getStatusType(row.value7)">
        {{ row.value7 }}
      </el-tag>
    </template>

    <!-- 文件格式列自定义渲染 -->
    <template #table:value6:simple="{ row }">
      <el-tag type="primary">
        {{ row.value6 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleView(row)">查看</el-button>
      <el-button type="text" size="mini" @click="handleEdit(row)">编辑</el-button>
      <el-button type="text" size="mini" @click="handleDelete(row)">删除</el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { fileFormat, fileStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoFileManagement',
  data() {
    return {
      tableType: 'video_file'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频文件管理',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        searchProps: {
          model: [
            {
              field: 'value3',
              label: '文件名',
              component: 'el-input',
              props: {
                placeholder: '请输入文件名'
              }
            },
            {
              field: 'value6',
              label: '文件格式',
              component: 'el-select',
              props: {
                placeholder: '请选择文件格式',
                clearable: true
              },
              options: [
                { label: '全部格式', value: '' },
                ...fileFormat
              ]
            },
            {
              field: 'value7',
              label: '文件状态',
              component: 'el-select',
              props: {
                placeholder: '请选择文件状态',
                clearable: true
              },
              options: [
                { label: '全部状态', value: '' },
                ...fileStatus
              ]
            },
            {
              field: 'dateRange',
              label: '生成时间',
              component: 'el-date-picker',
              props: {
                type: 'daterange',
                rangeSeparator: '至',
                startPlaceholder: '开始日期',
                endPlaceholder: '结束日期',
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd'
              }
            }
          ]
        },
        model: [
          {
            field: 'id',
            label: '视频文件ID',
            width: 120,
            fixed: 'left'
          },
          {
            field: 'value1',
            label: '任务编号',
            width: 150
          },
          {
            field: 'value3',
            label: '视频文件名',
            width: 200,
            showOverflowTooltip: true
          },
          {
            field: 'value4',
            label: '文件存储路径',
            width: 250,
            showOverflowTooltip: true
          },
          {
            field: 'value5',
            label: '文件大小',
            width: 120
          },
          {
            field: 'value6',
            label: '文件格式',
            width: 100
          },
          {
            field: 'value8',
            label: '采集来源',
            width: 150
          },
          {
            field: 'value7',
            label: '文件状态',
            width: 100
          },
          {
            field: 'value9',
            label: '文件生成时间',
            width: 160
          },
          {
            field: 'value10',
            label: '存储时长（天）',
            width: 120
          },
          {
            field: 'value11',
            label: '时长',
            width: 100
          },
          {
            field: 'value12',
            label: '分辨率',
            width: 120
          },
          {
            field: 'value13',
            label: '帧率',
            width: 100
          },
          {
            field: 'value14',
            label: '解码检测',
            width: 100
          },
          {
            field: 'value15',
            label: '时长检测',
            width: 100
          },
          {
            field: 'value16',
            label: '检测时间',
            width: 160
          }
        ],
        formProps: {
          model: [
          {
            field: 'value1',
            label: '任务编号',
            component: 'el-input',
            props: {
              placeholder: '请输入任务编号'
            },
            rules: [
              { required: true, message: '请输入任务编号', trigger: 'blur' }
            ]
          },
          {
            field: 'value3',
            label: '视频文件名',
            component: 'el-input',
            props: {
              placeholder: '请输入视频文件名'
            },
            rules: [
              { required: true, message: '请输入视频文件名', trigger: 'blur' }
            ]
          },
          {
            field: 'value4',
            label: '文件存储路径',
            component: 'el-input',
            props: {
              placeholder: '请输入文件存储路径'
            },
            rules: [
              { required: true, message: '请输入文件存储路径', trigger: 'blur' }
            ]
          },
          {
            field: 'value5',
            label: '文件大小',
            component: 'el-input',
            props: {
              placeholder: '请输入文件大小'
            }
          },
          {
            field: 'value6',
            label: '文件格式',
            component: 'el-select',
            props: {
              placeholder: '请选择文件格式'
            },
            options: fileFormat,
            rules: [
              { required: true, message: '请选择文件格式', trigger: 'change' }
            ]
          },
          {
            field: 'value8',
            label: '采集来源',
            component: 'el-input',
            props: {
              placeholder: '请输入采集来源'
            }
          },
          {
            field: 'value7',
            label: '文件状态',
            component: 'el-select',
            props: {
              placeholder: '请选择文件状态'
            },
            options: fileStatus,
            rules: [
              { required: true, message: '请选择文件状态', trigger: 'change' }
            ]
          },
          {
            field: 'value9',
            label: '文件生成时间',
            component: 'el-date-picker',
            props: {
              type: 'datetime',
              placeholder: '请选择文件生成时间',
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss'
            }
          },
          {
            field: 'value10',
            label: '存储时长（天）',
            component: 'el-input-number',
            props: {
              placeholder: '请输入存储时长',
              min: 1
            }
          },
          {
            field: 'value11',
            label: '时长',
            component: 'el-input',
            props: {
              placeholder: '请输入时长'
            }
          },
          {
            field: 'value12',
            label: '分辨率',
            component: 'el-input',
            props: {
              placeholder: '请输入分辨率'
            }
          },
          {
            field: 'value13',
            label: '帧率',
            component: 'el-input',
            props: {
              placeholder: '请输入帧率'
            }
          },
          {
            field: 'value14',
            label: '解码检测',
            component: 'el-input',
            props: {
              placeholder: '请输入解码检测结果'
            }
          },
          {
            field: 'value15',
            label: '时长检测',
            component: 'el-input',
            props: {
              placeholder: '请输入时长检测结果'
            }
          },
          {
            field: 'value16',
            label: '检测时间',
            component: 'el-date-picker',
            props: {
              type: 'datetime',
              placeholder: '请选择检测时间',
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss'
            }
          }
        ]
        }
      }
    }
  },
  methods: {
    getStatusType(status) {
      const statusMap = {
        '有效': 'success',
        '已删除': 'info',
        '损坏': 'danger',
        '待归档': 'warning'
      }
      return statusMap[status] || 'info'
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleView(row) {
      this.$refs.sheetRef.handleInfo(row)
    },
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handleDelete(row) {
      this.$refs.sheetRef.handleRemove([row.id])
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>

# 视频数据管理平台开发计划

## 一、项目背景

1. **项目用途**
   本项目为投标阶段的功能演示系统，旨在覆盖评标要求的关键业务点，展示平台在视频数据管理方面的能力。

2. **技术架构与复用说明**
   当前前端框架复用了以往演示项目的架构，项目中保留了部分旧业务页面，开发新功能时可参考其中实现方式。

## 二、开发规范

1. **目录结构**

   * 页面开发路径：`src/views/video/`
   * 接口定义路径：`src/api/video/`

2. **通用页面开发要求**

   * 普通增删查改类页面统一使用 `<EleSheet />` 组件，确保交互一致性与开发效率。

3. **接口与数据结构说明**

   * 所有模块统一调用 `src/api/demo/index.js` 中的接口。
   * 后端采用**统一宽表**设计，不同模块通过传递 `type` 参数区分业务。
   * 数据字段采用通用命名方式：`value1`、`value2`、`value3`、`value4` 等。

## 三、业务与菜单结构

---

### 1. 视频数据汇聚

路径：`src/views/video/collection`

#### 1.1 视频文件管理（type = video_file）

路径：`src/views/video/collection/file`

* 查询区域

- 文件名
- 文件格式
- 文件状态：全部状态 
- 生成时间：开始日期-结束日期 
- 操作：重置、搜索 

* 工具栏功能

- 批量删除
- 刷新 

* 列表数据

- 视频文件 ID
- 任务编号
- 视频文件名
- 文件存储路径
- 文件大小
- 文件格式
- 采集来源
- 文件状态
- 文件生成时间
- 存储时长（天）
- 时长
- 分辨率
- 帧率
- 解码检测
- 时长检测
- 检测时间
- 操作项（查看、编辑、删除 ） 

* 字段字典说明

- 文件格式：MP4、AVI、FLV、MOV 
- 文件状态：有效、已删除、损坏、待归档 

#### 1.2 采集任务管理

路径：`src/views/video/collection/task`

* **查询区域**

  * 任务名称：请输入任务名称
  * 任务状态：全部状态
  * 采集源：全部来源
  * 操作按钮：重置、查询

* **工具栏功能**

  * 创建新任务
  * 批量删除

* **列表字段**

  * 任务 ID、任务名称、任务编号
  * 采集协议、视频源配置、视频源描述
  * 采集开始时间、采集结束时间、采集帧率
  * 状态、调度策略、异常重试、任务失败原因
  * 分辨率、视频编码格式、采集日志 ID
  * 创建时间、最后更新时间、创建人
  * 操作项（查看、编辑、删除、分发）

* **字段字典**

  * 任务状态：运行中、已停止、异常、待执行
  * 采集源类型：RTSP 流、FTP 文件、本地设备
  * 采集协议：RTSP、FTP、SFTP、本地设备、RT SPS、其他

* **说明补充**

  * **分发任务说明**：前端每 5 秒轮询生成记录，用于服务指标统计展示。
  * **视频文件生成说明**：系统根据调度策略自动执行任务生成视频文件。

#### 1.3 服务指标统计

路径：`src/views/video/collection/service`

（待补充）

#### 1.4 价值萃取任务

路径：`src/views/video/collection/extraction`

（待补充）

#### 1.5 质量检测任务

路径：`src/views/video/collection/quality`

（待补充）

#### 1.6 采集参数配置

路径：`src/views/video/collection/config`

（待补充）

---

### 2. 视频数据处理

路径：`src/views/video/process`

#### 2.1 算法模型管理

路径：`src/views/video/process/model`

#### 2.2 任务编排管理

路径：`src/views/video/process/template`

#### 2.3 任务实例管理

路径：`src/views/video/process/task`

---

### 3. 视频数据标注

路径：`src/views/video/annotation`

> 子菜单待定

---

### 4. 视频数据管控

路径：`src/views/video/control`

> 子菜单待定